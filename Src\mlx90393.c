/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    mlx90393.c
  * @brief   MLX90393 三轴磁场传感器驱动实现
  ******************************************************************************
  * @attention
  *
  * MLX90393 是一款高精度的三轴磁场传感器，支持I2C通信
  * 本驱动实现基本的磁场测量功能
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "mlx90393.h"
#include "gpio.h"
#include <stdio.h>
#include <string.h>
#include <stdbool.h>
/* Private defines -----------------------------------------------------------*/
#define MLX90393_TIMEOUT        1000    // I2C超时时间 (ms)
#define MLX90393_CONVERSION_DELAY 10    // 转换延时 (ms)

/* Private variables ---------------------------------------------------------*/
static MLX90393_Handle_t mlx_handle;

/* 校准和统计变量 */
static MLX90393_Data_t offset_data = {0, 0, 0, 0};
static MLX90393_Data_t min_data = {0, 0, 0, 0};
static MLX90393_Data_t max_data = {0, 0, 0, 0};
static uint32_t measurement_count = 0;

/* Private function prototypes -----------------------------------------------*/
static uint8_t MLX90393_SendCommand(MLX90393_Handle_t *mlx, uint8_t cmd);
static uint8_t MLX90393_SendCommandWithData(MLX90393_Handle_t *mlx, uint8_t cmd, uint8_t data);
static float MLX90393_ConvertMagneticField(uint16_t raw_data, uint8_t gain, uint8_t resolution, uint8_t is_z_axis);
static float MLX90393_ConvertTemperature(uint16_t raw_data);

/**
  * @brief  初始化MLX90393传感器
  * @param  mlx: MLX90393句柄指针
  * @param  hi2c: I2C句柄指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_Init(MLX90393_Handle_t *mlx, I2C_HandleTypeDef *hi2c)
{
    uint8_t status;
    HAL_StatusTypeDef hal_result;

    // 静默初始化

    // 初始化句柄
    mlx->hi2c = hi2c;
    mlx->address = MLX90393_I2C_ADDR_SHIFTED;
    mlx->gain_sel = 0;  // 默认增益
    mlx->res_x = 0;     // 默认分辨率
    mlx->res_y = 0;
    mlx->res_z = 0;
    mlx->osr = 0;       // 默认过采样率

    // 检查设备是否响应
    hal_result = HAL_I2C_IsDeviceReady(mlx->hi2c, mlx->address, 3, 1000);
    if (hal_result != HAL_OK) {
        return 1;
    }

    // 复位传感器
    if (MLX90393_Reset(mlx) != 0) {
        return 1;
    }

    HAL_Delay(50);  // 等待复位完成

    // 检查通信
    status = MLX90393_GetStatus(mlx);
    if (status == 0xFF) {
        return 1;
    }

    // 配置寄存器0 (增益和HALLCONF)
    uint16_t reg0_val;
    if (MLX90393_ReadRegister(mlx, 0x00, &reg0_val) == 0) {
        // 修改增益设置 (根据示例，GAIN_SEL在bit[7:5])
        uint16_t new_reg0 = (reg0_val & 0xFF1F) | (MLX90393_GAIN_SEL << 5);
        if (MLX90393_WriteRegister(mlx, 0x00, new_reg0) == 0) {
            mlx->gain_sel = MLX90393_GAIN_SEL;
        }
    }

    // 配置寄存器2 (分辨率和过采样) - 修正位域定义
    uint16_t reg2_val;
    if (MLX90393_ReadRegister(mlx, 0x02, &reg2_val) == 0) {
        // 构建新的REG2值 - 使用正确的位域
        uint16_t new_reg2 = 0;
        new_reg2 |= (MLX90393_RES_Z & 0x3) << 12;   // RES_Z[1:0] -> bit[13:12]
        new_reg2 |= (MLX90393_RES_Y & 0x3) << 10;   // RES_Y[1:0] -> bit[11:10]
        new_reg2 |= (MLX90393_RES_X & 0x3) << 8;    // RES_X[1:0] -> bit[9:8]
        new_reg2 |= (MLX90393_DIG_FILT & 0x7) << 5; // DIG_FILT[2:0] -> bit[7:5]
        new_reg2 |= (MLX90393_OSR & 0x3);           // OSR[1:0] -> bit[1:0]

        if (MLX90393_WriteRegister(mlx, 0x02, new_reg2) == 0) {
            mlx->res_x = MLX90393_RES_X;
            mlx->res_y = MLX90393_RES_Y;
            mlx->res_z = MLX90393_RES_Z;
            mlx->osr = MLX90393_OSR;
//            printf("REG2 updated successfully: 0x%04X\r\n", new_reg2);
        } else {
//            printf("Failed to update REG2\r\n");
        }
    }

//    printf("MLX90393_Init: Using single measurement mode\r\n");

    // 显示当前配置摘要
//    printf("\r\n=== Current Configuration Summary ===\r\n");
//    printf("GAIN_SEL: %d, RES_X: %d, RES_Y: %d, RES_Z: %d\r\n",
//           MLX90393_GAIN_SEL, MLX90393_RES_X, MLX90393_RES_Y, MLX90393_RES_Z);
//    printf("OSR: %d, DIG_FILT: %d\r\n", MLX90393_OSR, MLX90393_DIG_FILT);
//    printf("Data shifts: X>>%d, Y>>%d, Z>>%d\r\n",
//           MLX90393_X_SHIFT, MLX90393_Y_SHIFT, MLX90393_Z_SHIFT);
//    printf("=====================================\r\n\r\n");

    return 0;
}

/**
  * @brief  复位MLX90393传感器
  * @param  mlx: MLX90393句柄指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_Reset(MLX90393_Handle_t *mlx)
{
    return MLX90393_SendCommand(mlx, MLX90393_CMD_RESET);
}

/**
  * @brief  开始测量
  * @param  mlx: MLX90393句柄指针
  * @param  axis: 要测量的轴 (MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z | MLX90393_AXIS_T)
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_StartMeasurement(MLX90393_Handle_t *mlx, uint8_t axis)
{
    uint8_t result;
    printf("StartMeasurement: Starting measurement for axis 0x%02X\r\n", axis);

    result = MLX90393_SendCommandWithData(mlx, MLX90393_CMD_START_MEASURE, axis);

    if (result == 0) {
        printf("StartMeasurement: Command sent successfully\r\n");
    } else {
        printf("StartMeasurement: Command failed\r\n");
    }

    return result;
}

/**
  * @brief  读取测量结果
  * @param  mlx: MLX90393句柄指针
  * @param  data: 数据结构指针
  * @param  axis: 要读取的轴
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_ReadMeasurement(MLX90393_Handle_t *mlx, MLX90393_Data_t *data, uint8_t axis)
{
    uint8_t tx_data[2];
    uint8_t rx_data[9];  // 最多8字节数据 + 1字节状态
    uint8_t data_count = 0;
    uint32_t raw_x, raw_y, raw_z, raw_t;
    HAL_StatusTypeDef hal_result;

    printf("ReadMeasurement: Reading axis 0x%02X\r\n", axis);

    // 准备读取命令
    tx_data[0] = MLX90393_CMD_READ_MEASURE;
    tx_data[1] = axis;

    // 计算预期的数据字节数
    if (axis & MLX90393_AXIS_X) data_count += 2;
    if (axis & MLX90393_AXIS_Y) data_count += 2;
    if (axis & MLX90393_AXIS_Z) data_count += 2;
    if (axis & MLX90393_AXIS_T) data_count += 2;

    printf("ReadMeasurement: Expected data bytes: %d\r\n", data_count);

    // 发送读取命令
    printf("ReadMeasurement: Sending read command...\r\n");
    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 2, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        printf("ReadMeasurement: Failed to send read command (HAL: %d)\r\n", hal_result);
        return 1;
    }

    // 接收数据
    printf("ReadMeasurement: Receiving data...\r\n");
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, data_count + 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        printf("ReadMeasurement: Failed to receive data (HAL: %d)\r\n", hal_result);
        return 1;
    }

    printf("ReadMeasurement: Received status: 0x%02X\r\n", rx_data[0]);
    printf("ReadMeasurement: Raw data: ");
    for (int i = 1; i <= data_count; i++) {
        printf("0x%02X ", rx_data[i]);
    }
    printf("\r\n");

    // 解析数据
    uint8_t idx = 1;  // 跳过状态字节

    if (axis & MLX90393_AXIS_X) {
        raw_x = (rx_data[idx] << 8) | rx_data[idx + 1];
        data->x = MLX90393_ConvertMagneticField(raw_x, mlx->gain_sel, mlx->res_x, 0);  // X轴，is_z_axis=0
        idx += 2;
    }

    if (axis & MLX90393_AXIS_Y) {
        raw_y = (rx_data[idx] << 8) | rx_data[idx + 1];
        data->y = MLX90393_ConvertMagneticField(raw_y, mlx->gain_sel, mlx->res_y, 0);  // Y轴，is_z_axis=0
        idx += 2;
    }

    if (axis & MLX90393_AXIS_Z) {
        raw_z = (rx_data[idx] << 8) | rx_data[idx + 1];
        data->z = MLX90393_ConvertMagneticField(raw_z, mlx->gain_sel, mlx->res_z, 1);  // Z轴，is_z_axis=1
        idx += 2;
    }

    if (axis & MLX90393_AXIS_T) {
        raw_t = (rx_data[idx] << 8) | rx_data[idx + 1];
        data->temp = MLX90393_ConvertTemperature(raw_t);
        idx += 2;
    }

    return 0;
}

/**
  * @brief  执行单次完整测量 (参考新示例代码的单次测量模式)
  * @param  mlx: MLX90393句柄指针
  * @param  data: 数据结构指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_ReadSingleMeasurement(MLX90393_Handle_t *mlx, MLX90393_Data_t *data)
{
    HAL_StatusTypeDef hal_result;
    uint8_t tx_data[2];
    uint8_t rx_data[9];  // 状态字节 + 8字节数据 (TXYZ各2字节)
    uint32_t raw_x, raw_y, raw_z;

    // 步骤1：发送单次测量命令 - 使用正确的轴选择位
    uint8_t axes = MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z;  // 0x0E
    tx_data[0] = 0x30 | axes;  // MLX90393_CMD_SM | axes

    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 接收状态字节
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 检查错误
    if (rx_data[0] & 0x10) {  // ERROR_BIT
        return 1;
    }

    // 等待测量完成
    HAL_Delay(10);

    // 步骤2：读取测量结果
    tx_data[0] = 0x40 | axes;  // MLX90393_CMD_RM | axes

    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 读取数据：状态字节 + TXYZ数据（按照选择的轴）
    // 由于我们选择了XYZ（没有T），所以是 1状态 + 6数据字节
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 7, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 检查读取状态
    if (rx_data[0] & 0x10) {  // ERROR_BIT
        printf("RM command error: 0x%02X\r\n", rx_data[0]);
        return 1;
    }

    // 解析数据：按照XYZ顺序（没有T）
    // X: rx_data[1](高) + rx_data[2](低)
    // Y: rx_data[3](高) + rx_data[4](低)
    // Z: rx_data[5](高) + rx_data[6](低)

    raw_x = (rx_data[1] << 8) | rx_data[2];
    raw_y = (rx_data[3] << 8) | rx_data[4];
    raw_z = (rx_data[5] << 8) | rx_data[6];

    // 删除调试输出

    // 使用宏定义的位移设置来修正数据
    uint32_t corrected_x = raw_x >> MLX90393_X_SHIFT;  // X轴位移
    uint32_t corrected_y = raw_y >> MLX90393_Y_SHIFT;  // Y轴位移
    uint32_t corrected_z = raw_z >> MLX90393_Z_SHIFT;  // Z轴位移

    // 使用修正后的数据进行转换
    data->x = MLX90393_ConvertMagneticField(corrected_x, mlx->gain_sel, mlx->res_x, 0);  // X轴，is_z_axis=0
    data->y = MLX90393_ConvertMagneticField(corrected_y, mlx->gain_sel, mlx->res_y, 0);  // Y轴，is_z_axis=0
    data->z = MLX90393_ConvertMagneticField(corrected_z, mlx->gain_sel, mlx->res_z, 1);  // Z轴，is_z_axis=1
    data->temp = 0.0f;  // 没有读取温度

    // X轴偏移补偿 (去掉固定的高位偏移)
    #if MLX90393_X_OFFSET_ENABLE
    data->x -= MLX90393_X_OFFSET_VALUE;
    #endif

    // 应用偏移补偿
    data->x -= offset_data.x;
    data->y -= offset_data.y;
    data->z -= offset_data.z;

    // 更新统计信息
    measurement_count++;
    if (measurement_count == 1) {
        min_data = max_data = *data;
    } else {
        if (data->x < min_data.x) min_data.x = data->x;
        if (data->x > max_data.x) max_data.x = data->x;
        if (data->y < min_data.y) min_data.y = data->y;
        if (data->y > max_data.y) max_data.y = data->y;
        if (data->z < min_data.z) min_data.z = data->z;
        if (data->z > max_data.z) max_data.z = data->z;
    }

    return 0;
}

/**
  * @brief  执行单次完整测量 (修复版本)
  * @param  mlx: MLX90393句柄指针
  * @param  data: 数据结构指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_ReadSingleMeasurement_Fixed(MLX90393_Handle_t *mlx, MLX90393_Data_t *data)
{
    HAL_StatusTypeDef hal_result;
    uint8_t tx_data[2];
    uint8_t rx_data[9];  // 状态字节 + 8字节数据 (TXYZ各2字节)
    uint32_t raw_x, raw_y, raw_z;

    // 步骤1：发送单次测量命令 - 使用正确的轴选择位
    uint8_t axes = MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z;  // 0x0E
    tx_data[0] = 0x30 | axes;  // MLX90393_CMD_SM | axes

    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 接收状态字节
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 检查错误
    if (rx_data[0] & 0x10) {  // ERROR_BIT
        return 1;
    }

    // 等待测量完成
    HAL_Delay(10);

    // 步骤2：读取测量结果
    tx_data[0] = 0x40 | axes;  // MLX90393_CMD_RM | axes

    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 读取数据：状态字节 + XYZ数据（按照T-X-Y-Z顺序返回，但我们没选择T）
    // 由于我们选择了XYZ（没有T），返回顺序是：X-Y-Z
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 7, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 检查读取状态
    if (rx_data[0] & 0x10) {  // ERROR_BIT
        printf("RM command error: 0x%02X\r\n", rx_data[0]);
        return 1;
    }

    // 正确解析数据：由于没有选择温度，数据顺序就是X-Y-Z
    raw_x = (rx_data[1] << 8) | rx_data[2];  // X轴数据
    raw_y = (rx_data[3] << 8) | rx_data[4];  // Y轴数据
    raw_z = (rx_data[5] << 8) | rx_data[6];  // Z轴数据

//    printf("Raw data: X=0x%04X Y=0x%04X Z=0x%04X\r\n", raw_x, raw_y, raw_z);

    // 应用位移修正
    uint32_t corrected_x = raw_x >> MLX90393_X_SHIFT;
    uint32_t corrected_y = raw_y >> MLX90393_Y_SHIFT;
    uint32_t corrected_z = raw_z >> MLX90393_Z_SHIFT;

    // 转换为物理值
    data->x = MLX90393_ConvertMagneticField(corrected_x, mlx->gain_sel, mlx->res_x, 0);
    data->y = MLX90393_ConvertMagneticField(corrected_y, mlx->gain_sel, mlx->res_y, 0);
    data->z = MLX90393_ConvertMagneticField(corrected_z, mlx->gain_sel, mlx->res_z, 1);
    data->temp = 0.0f;

    // 应用偏移补偿
    #if MLX90393_X_OFFSET_ENABLE
    data->x -= MLX90393_X_OFFSET_VALUE;
    #endif

    data->x -= offset_data.x;
    data->y -= offset_data.y;
    data->z -= offset_data.z;

    return 0;
}

/**
  * @brief  读取寄存器
  * @param  mlx: MLX90393句柄指针
  * @param  reg_addr: 寄存器地址
  * @param  reg_data: 寄存器数据指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_ReadRegister(MLX90393_Handle_t *mlx, uint8_t reg_addr, uint16_t *reg_data)
{
    uint8_t tx_data[2];
    uint8_t rx_data[3];

    tx_data[0] = MLX90393_CMD_READ_REG;
    tx_data[1] = reg_addr << 2;  // 寄存器地址需要左移2位

    if (HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 2, MLX90393_TIMEOUT) != HAL_OK) {
        return 1;
    }

    if (HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 3, MLX90393_TIMEOUT) != HAL_OK) {
        return 1;
    }

    *reg_data = (rx_data[1] << 8) | rx_data[2];
    return 0;
}

/**
  * @brief  写入寄存器
  * @param  mlx: MLX90393句柄指针
  * @param  reg_addr: 寄存器地址
  * @param  reg_data: 要写入的数据
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_WriteRegister(MLX90393_Handle_t *mlx, uint8_t reg_addr, uint16_t reg_data)
{
    uint8_t tx_data[4];
    uint8_t rx_data[1];

    tx_data[0] = MLX90393_CMD_WRITE_REG;
    tx_data[1] = (reg_data >> 8) & 0xFF;    // 高字节
    tx_data[2] = reg_data & 0xFF;           // 低字节
    tx_data[3] = reg_addr << 2;             // 寄存器地址需要左移2位

    if (HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 4, MLX90393_TIMEOUT) != HAL_OK) {
        return 1;
    }

    if (HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT) != HAL_OK) {
        return 1;
    }

    return 0;
}

/**
  * @brief  获取传感器状态
  * @param  mlx: MLX90393句柄指针
  * @retval 状态字节，0xFF表示通信失败
  */
uint8_t MLX90393_GetStatus(MLX90393_Handle_t *mlx)
{
    uint8_t status = 0xFF;
    HAL_StatusTypeDef hal_result;
    uint8_t cmd = MLX90393_CMD_NOP;
    uint8_t rx_data[1];

    // 发送NOP命令
    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, &cmd, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        return 0xFF;
    }

    // 接收状态字节
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        return 0xFF;
    }

    status = rx_data[0];

    return status;
}

/**
  * @brief  MLX90393功能测试函数
  */
void MLX90393_Test(void)
{
    MLX90393_Data_t sensor_data;
    uint8_t result;
    uint32_t test_count = 0;
    uint8_t init_retry = 0;

    // 尝试初始化传感器，最多重试3次
    do {
        init_retry++;
        result = MLX90393_Init(&mlx_handle, &hi2c1);
        if (result == 0) {
            break;
        } else {
            if (init_retry < 3) {
                HAL_Delay(100);
            }
        }
    } while (init_retry < 3);

    if (result != 0) {
        // 进入错误指示模式
        while (1) {
            LED_ON;
            HAL_Delay(200);
            LED_OFF;
            HAL_Delay(200);
            HAL_Delay(2000);
        }
    }

    // 运行调试函数来验证修复效果
//    printf("=== Running Debug Test ===\r\n");
//    MLX90393_DebugRawData(&mlx_handle);

    // 配置WOC模式
    if (MLX90393_WOC_ENABLE) {
        MLX90393_ConfigureWOC(&mlx_handle);

        // 显示当前WOC配置 - 三轴都启用
        printf("WOC Config: X+Y+Z axes, XY_threshold=%d Z_threshold=%d LSB\r\n",
               MLX90393_WOC_XY_THRESHOLD, MLX90393_WOC_Z_THRESHOLD);

        // 启动WOC模式 - 使用三轴选择位
        uint8_t xyz_axes = MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z;  // 0x0E
        uint8_t woc_cmd[2] = {MLX90393_CMD_WOC, xyz_axes};
        HAL_I2C_Master_Transmit(mlx_handle.hi2c, mlx_handle.address, woc_cmd, 2, MLX90393_TIMEOUT);
        HAL_Delay(10);
        uint8_t woc_status;
        HAL_I2C_Master_Receive(mlx_handle.hi2c, mlx_handle.address, &woc_status, 1, MLX90393_TIMEOUT);
    }

    while (1) {
        // 检查WOC中断状态
        if (MLX90393_WOC_ENABLE) {
            if (MLX90393_CheckWOCStatus(&mlx_handle)) {
                // WOC中断已处理，继续下一次循环
                LED_TOGGLE;  // WOC中断指示
                HAL_Delay(100);
                continue;
            }
        }

        // 执行单次测量 - 使用修复后的函数
        result = MLX90393_ReadSingleMeasurement_Fixed(&mlx_handle, &sensor_data);

        if (result == 0) {
            test_count++;
            // 只显示磁场强度值
            printf("X:%6.1f Y:%6.1f Z:%5.1f\r\n",
                   sensor_data.x, sensor_data.y, sensor_data.z);

            // LED指示
            if (test_count % 20 == 0) {
                LED_TOGGLE;
            }
        } else {

						static bool int_mode_printed = false;

						if (!int_mode_printed) {
								printf("INT_MODE.....\r\n");
								int_mode_printed = true;
						}
						LED_TOGGLE;

        }

        // 高频测量 - 减少延时
        HAL_Delay(50);  // 20Hz 测量频率
    }
}

/* Private function implementations ------------------------------------------*/

/**
  * @brief  发送单字节命令
  * @param  mlx: MLX90393句柄指针
  * @param  cmd: 命令字节
  * @retval 0: 成功, 1: 失败
  */
static uint8_t MLX90393_SendCommand(MLX90393_Handle_t *mlx, uint8_t cmd)
{
    uint8_t tx_data[1];
    uint8_t rx_data[1];

    tx_data[0] = cmd;

    if (HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT) != HAL_OK) {
        return 1;
    }

    // 大部分命令都会返回状态字节
    if (HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT) != HAL_OK) {
        return 1;
    }

    return 0;
}

/**
  * @brief  发送带数据的命令
  * @param  mlx: MLX90393句柄指针
  * @param  cmd: 命令字节
  * @param  data: 数据字节
  * @retval 0: 成功, 1: 失败
  */
static uint8_t MLX90393_SendCommandWithData(MLX90393_Handle_t *mlx, uint8_t cmd, uint8_t data)
{
    uint8_t tx_data[2];
    uint8_t rx_data[1];
    HAL_StatusTypeDef hal_result;

    printf("SendCommandWithData: CMD=0x%02X, DATA=0x%02X\r\n", cmd, data);

    tx_data[0] = cmd;
    tx_data[1] = data;

    // 发送命令
    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 2, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        printf("SendCommandWithData: Transmit failed (HAL: %d)\r\n", hal_result);
        return 1;
    }

    // 接收状态字节
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        printf("SendCommandWithData: Receive failed (HAL: %d)\r\n", hal_result);
        return 1;
    }

    printf("SendCommandWithData: Status received: 0x%02X\r\n", rx_data[0]);
    return 0;
}

/**
  * @brief  转换原始磁场数据为微特斯拉 (uT) - 参考示例代码
  * @param  raw_data: 原始16位数据
  * @param  gain: 增益设置 (0-7)
  * @param  resolution: 分辨率设置 (0-3)
  * @retval 磁场强度 (uT)
  */
static float MLX90393_ConvertMagneticField(uint16_t raw_data, uint8_t gain, uint8_t resolution, uint8_t is_z_axis)
{
    // 根据寄存器文档的灵敏度表 (HALLCONF=0xC)
    float xy_sensitivity_table[8] = {0.751f, 0.601f, 0.451f, 0.376f, 0.300f, 0.250f, 0.200f, 0.150f};
    float z_sensitivity_table[8] = {1.210f, 0.968f, 0.726f, 0.605f, 0.484f, 0.403f, 0.323f, 0.242f};

    // 选择正确的灵敏度
    float base_sensitivity = is_z_axis ? z_sensitivity_table[gain & 0x7] : xy_sensitivity_table[gain & 0x7];

    // 根据分辨率调整灵敏度
    float actual_sensitivity = base_sensitivity * (1 << resolution);

    // 转换原始数据
    float result;

    switch(resolution) {
        case 0:
        case 1:
            // res = 0 或 1: 使用 (int16_t)(raw_data)
            result = (int16_t)(raw_data) * actual_sensitivity;
            break;
        case 2:
            // res = 2: 使用 (raw_data - 32768.f)
            result = ((float)raw_data - 32768.0f) * actual_sensitivity;
            break;
        case 3:
            // res = 3: 使用 (raw_data - 16384.f)
            result = ((float)raw_data - 16384.0f) * actual_sensitivity;
            break;
        default:
            result = (int16_t)(raw_data) * actual_sensitivity;
            break;
    }

    return result;
}

/**
  * @brief  转换原始温度数据为摄氏度
  * @param  raw_data: 原始16位温度数据
  * @retval 温度 (°C)
  */
static float MLX90393_ConvertTemperature(uint16_t raw_data)
{
    // MLX90393温度转换公式 (根据数据手册)
    // T(°C) = ((TDATA - TREF) / TSENS) + 25°C
    // 其中 TREF ≈ 46244, TSENS ≈ 45.2 LSB/°C

    // 但根据实际测试，可能需要调整参数
    // 使用更通用的线性转换
    float temp_celsius = ((float)raw_data - 46244.0f) / 45.2f + 25.0f;

    // 如果温度值看起来不合理，尝试其他转换公式
    if (temp_celsius < -40.0f || temp_celsius > 85.0f) {
        // 备用转换公式
        temp_celsius = ((float)raw_data / 256.0f) - 273.15f;
    }

    return temp_celsius;
}

/* 诊断和调试函数实现 ------------------------------------------------*/

/**
  * @brief  I2C总线扫描函数
  * @param  hi2c: I2C句柄指针
  */
void I2C_Scanner(I2C_HandleTypeDef *hi2c)
{
    printf("\r\n=== I2C Bus Scanner ===\r\n");
    printf("Scanning I2C bus for devices...\r\n");

    uint8_t devices_found = 0;

    for (uint8_t addr = 1; addr < 128; addr++) {
        // 尝试发送地址，看是否有ACK响应
        HAL_StatusTypeDef result = HAL_I2C_IsDeviceReady(hi2c, addr << 1, 1, 100);

        if (result == HAL_OK) {
            printf("Device found at address 0x%02X (7-bit: 0x%02X)\r\n", addr << 1, addr);
            devices_found++;
        }

        HAL_Delay(1);  // 短暂延时避免总线拥塞
    }

    if (devices_found == 0) {
        printf("No I2C devices found!\r\n");
        printf("Please check:\r\n");
        printf("1. I2C wiring (SDA, SCL)\r\n");
        printf("2. Pull-up resistors (typically 4.7k ohm)\r\n");
        printf("3. Device power supply\r\n");
        printf("4. I2C clock configuration\r\n");
    } else {
        printf("Total devices found: %d\r\n", devices_found);
    }

    printf("=== I2C Scan Complete ===\r\n\r\n");
}

/**
  * @brief  MLX90393通信测试
  * @param  mlx: MLX90393句柄指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_TestCommunication(MLX90393_Handle_t *mlx)
{
    HAL_StatusTypeDef hal_result;

    printf("Testing MLX90393 communication...\r\n");

    // 测试1: 检查设备是否响应
    printf("Test 1: Device ready check...\r\n");
    hal_result = HAL_I2C_IsDeviceReady(mlx->hi2c, mlx->address, 3, 1000);

    if (hal_result == HAL_OK) {
        printf("  PASS: Device responds to address 0x%02X\r\n", mlx->address);
    } else {
        printf("  FAIL: Device not responding (HAL Status: %d)\r\n", hal_result);
        return 1;
    }

    // 测试2: 尝试发送NOP命令
    printf("Test 2: NOP command test...\r\n");
    uint8_t cmd = MLX90393_CMD_NOP;
    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, &cmd, 1, 1000);

    if (hal_result == HAL_OK) {
        printf("  PASS: NOP command sent successfully\r\n");

        // 尝试读取状态字节
        uint8_t status;
        hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, &status, 1, 1000);

        if (hal_result == HAL_OK) {
            printf("  PASS: Status byte received: 0x%02X\r\n", status);
            return 0;
        } else {
            printf("  FAIL: Cannot read status byte (HAL Status: %d)\r\n", hal_result);
        }
    } else {
        printf("  FAIL: Cannot send NOP command (HAL Status: %d)\r\n", hal_result);
    }

    return 1;
}

/**
  * @brief  读取MLX90393当前配置
  * @param  mlx: MLX90393句柄指针
  * @retval 0: 成功, 1: 失败
  */
uint8_t MLX90393_ReadConfiguration(MLX90393_Handle_t *mlx)
{
    uint16_t reg_data;
    uint8_t result;

    printf("\r\n=== MLX90393 Configuration ===\r\n");

    // 读取寄存器0x00 (GAIN_SEL, HALLCONF)
    result = MLX90393_ReadRegister(mlx, 0x00, &reg_data);
    if (result == 0) {
        uint8_t gain_sel = (reg_data >> 4) & 0x07;
        uint8_t hallconf = (reg_data >> 0) & 0x0F;

        printf("Register 0x00: 0x%04X\r\n", reg_data);
        printf("  GAIN_SEL: %d (", gain_sel);
        switch(gain_sel) {
            case 0: printf("5x gain"); break;
            case 1: printf("4x gain"); break;
            case 2: printf("3x gain"); break;
            case 3: printf("2.5x gain"); break;
            case 4: printf("2x gain"); break;
            case 5: printf("1.67x gain"); break;
            case 6: printf("1.33x gain"); break;
            case 7: printf("1x gain"); break;
        }
        printf(")\r\n");
        printf("  HALLCONF: 0x%X\r\n", hallconf);

        // 更新句柄中的增益设置
        mlx->gain_sel = gain_sel;
    } else {
        printf("Failed to read register 0x00\r\n");
    }

    // 读取寄存器0x02 (分辨率设置) - 修正位域解析
    result = MLX90393_ReadRegister(mlx, 0x02, &reg_data);
    if (result == 0) {
        uint8_t res_x = (reg_data >> 8) & 0x03;    // RES_X[1:0] -> bit[9:8]
        uint8_t res_y = (reg_data >> 10) & 0x03;   // RES_Y[1:0] -> bit[11:10]
        uint8_t res_z = (reg_data >> 12) & 0x03;   // RES_Z[1:0] -> bit[13:12]
        uint8_t osr = (reg_data >> 0) & 0x03;      // OSR[1:0] -> bit[1:0]
        uint8_t dig_filt = (reg_data >> 5) & 0x07; // DIG_FILT[2:0] -> bit[7:5]

        printf("Register 0x02: 0x%04X\r\n", reg_data);
        printf("  RES_X: %d, RES_Y: %d, RES_Z: %d\r\n", res_x, res_y, res_z);
        printf("  OSR: %d, DIG_FILT: %d\r\n", osr, dig_filt);

        // 更新句柄中的分辨率设置
        mlx->res_x = res_x;
        mlx->res_y = res_y;
        mlx->res_z = res_z;
        mlx->osr = osr;
    } else {
        printf("Failed to read register 0x02\r\n");
    }

    printf("=== Configuration Complete ===\r\n\r\n");
    return 0;
}

/**
  * @brief  零点校准 - 将当前读数设为零点偏移
  * @param  mlx: MLX90393句柄指针
  */
void MLX90393_CalibrateZero(MLX90393_Handle_t *mlx)
{
    MLX90393_Data_t cal_data;
    uint8_t samples = 10;
    float sum_x = 0, sum_y = 0, sum_z = 0;

    printf("\r\n=== Zero Point Calibration ===\r\n");
    printf("Taking %d samples for calibration...\r\n", samples);

    for (int i = 0; i < samples; i++) {
        if (MLX90393_ReadSingleMeasurement(mlx, &cal_data) == 0) {
            sum_x += cal_data.x;
            sum_y += cal_data.y;
            sum_z += cal_data.z;
            printf("Sample %d: X:%.1f Y:%.1f Z:%.1f\r\n", i+1, cal_data.x, cal_data.y, cal_data.z);
        }
        HAL_Delay(100);
    }

    // 计算平均值作为偏移
    offset_data.x = sum_x / samples;
    offset_data.y = sum_y / samples;
    offset_data.z = sum_z / samples;

    printf("Calibration complete!\r\n");
    printf("Offset values: X:%.2f Y:%.2f Z:%.2f uT\r\n",
           offset_data.x, offset_data.y, offset_data.z);
    printf("=== Calibration Complete ===\r\n\r\n");
}

/**
  * @brief  显示测量统计信息
  */
void MLX90393_ShowStatistics(void)
{
    if (measurement_count == 0) {
        printf("No measurements taken yet.\r\n");
        return;
    }

    printf("\r\n=== Measurement Statistics ===\r\n");
    printf("Total measurements: %lu\r\n", measurement_count);
    printf("Range X: %.2f to %.2f uT (span: %.2f uT)\r\n",
           min_data.x, max_data.x, max_data.x - min_data.x);
    printf("Range Y: %.2f to %.2f uT (span: %.2f uT)\r\n",
           min_data.y, max_data.y, max_data.y - min_data.y);
    printf("Range Z: %.2f to %.2f uT (span: %.2f uT)\r\n",
           min_data.z, max_data.z, max_data.z - min_data.z);
    printf("Current offset: X:%.2f Y:%.2f Z:%.2f uT\r\n",
           offset_data.x, offset_data.y, offset_data.z);
    printf("=== Statistics Complete ===\r\n\r\n");
}

/**
 * @brief 配置WOC(Wake-up On Change)模式 - 低功耗版本
 * @param mlx MLX90393句柄
 * @retval 状态码
 */
uint8_t MLX90393_ConfigureWOC(MLX90393_Handle_t *mlx) {
    uint8_t status;
    uint16_t reg_data;

    // 1. 配置寄存器0x01 - 启用WOC_DIFF和TRIG_INT，设置低功耗模式
    status = MLX90393_ReadRegister(mlx, 0x01, &reg_data);
    if (status != MLX90393_OK) {
        return status;
    }

    // 清除相关位域
    reg_data &= ~((0x0F << 12) | (0x7F << 5) | (1 << 4) | (1 << 1));

    // 设置TRIG_INT = 1 (INT模式), WOC_DIFF = 1 (启用变化检测)
    reg_data |= (1 << 4);  // TRIG_INT_SEL = 1 (bit 4, 中断模式)
    reg_data |= (1 << 1);  // WOC_DIFF = 1 (bit 1, 启用变化检测)

    // 设置BURST_SEL启用X+Y+Z轴 (bit[15:12])
    // 设置X+Y+Z轴: bit3=Z, bit2=Y, bit1=X, bit0=T -> 0x0E
    reg_data |= (0x0E << 12);  // BURST_SEL = 0x0E (启用X+Y+Z轴)

    // 关键：设置BURST_DATA_RATE为非零值以启用低功耗间歇采样
    // BURST_DATA_RATE = 50 表示间隔 50 × 20ms = 1秒采样一次
    reg_data |= (50 << 5);  // BURST_DATA_RATE = 50 (bit[11:5])

    status = MLX90393_WriteRegister(mlx, 0x01, reg_data);
    if (status != MLX90393_OK) {
        return status;
    }

//    printf("MLX90393 Low Power Mode: BURST_DATA_RATE = 50 (1s interval)\r\n");

    // 2. 设置X、Y和Z轴阈值
    // 根据MLX90393数据手册，寄存器0x07控制X/Y轴阈值
//    printf("Setting XY threshold to %d\r\n", MLX90393_WOC_XY_THRESHOLD);
    status = MLX90393_WriteRegister(mlx, 0x07, MLX90393_WOC_XY_THRESHOLD);  // X/Y轴阈值
    if (status != MLX90393_OK) {
//        printf("Failed to write XY threshold\r\n");
        return status;
    }

    // 3. 设置Z轴阈值 (寄存器0x08)
//    printf("Setting Z threshold to %d\r\n", MLX90393_WOC_Z_THRESHOLD);
    status = MLX90393_WriteRegister(mlx, 0x08, MLX90393_WOC_Z_THRESHOLD);
    if (status != MLX90393_OK) {
//        printf("Failed to write Z threshold\r\n");
        return status;
    }

    // 4. 温度阈值设为最大，禁用温度监测
    status = MLX90393_WriteRegister(mlx, 0x09, 0xFFFF);
    if (status != MLX90393_OK) {
        return status;
    }

    // 5. 启动WOC模式
    uint8_t xyz_axes = MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z;  // 0x0E
    uint8_t woc_cmd[2] = {MLX90393_CMD_WOC, xyz_axes};
    HAL_StatusTypeDef hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, woc_cmd, 2, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        return MLX90393_ERROR;
    }

    HAL_Delay(10);

    // 读取WOC启动状态
    uint8_t woc_status;
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, &woc_status, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) {
        return MLX90393_ERROR;
    }

    // 检查WOC模式是否成功启动
    if ((woc_status & MLX90393_STATUS_WOC_MODE) == 0) {
        return MLX90393_ERROR;
    }

    return MLX90393_OK;
}

/**
 * @brief 配置MLX90393进入超低功耗模式
 * @param mlx MLX90393句柄
 * @retval 状态码
 */
uint8_t MLX90393_EnterLowPowerMode(MLX90393_Handle_t *mlx) {
    uint8_t status;
    uint16_t reg_data;

    // 1. 优化寄存器0x00 - 降低增益和HALLCONF以减少功耗
    status = MLX90393_ReadRegister(mlx, 0x00, &reg_data);
    if (status != MLX90393_OK) {
        return status;
    }

    // 使用头文件中定义的增益设置，修正位域为bit[7:5]
    reg_data &= ~((0x7 << 5) | (0xF << 0));  // 清除GAIN_SEL[7:5]和HALLCONF[3:0]位
    reg_data |= (MLX90393_GAIN_SEL << 5);   // 使用头文件中的增益设置
    reg_data |= (0 << 0);   // HALLCONF = 0 (最低频率)

//    printf("Setting GAIN_SEL to %d (bit[7:5])\r\n", MLX90393_GAIN_SEL);

    status = MLX90393_WriteRegister(mlx, 0x00, reg_data);
    if (status != MLX90393_OK) {
        return status;
    }

    // 2. 优化寄存器0x02 - 设置最低分辨率和过采样率
    status = MLX90393_ReadRegister(mlx, 0x02, &reg_data);
    if (status != MLX90393_OK) {
        return status;
    }

    // 设置最低分辨率和过采样率以减少功耗
    reg_data &= ~((0x3F << 10) | (0x3F << 4) | (0x7 << 2) | 0x3);
    reg_data |= (3 << 14);  // RES_Z = 3 (最低分辨率)
    reg_data |= (3 << 10);  // RES_Y = 3 (最低分辨率)
    reg_data |= (3 << 4);   // RES_X = 3 (最低分辨率)
    reg_data |= (0 << 2);   // DIG_FILT = 0 (最少滤波)
    reg_data |= (0);        // OSR = 0 (最低过采样)

    status = MLX90393_WriteRegister(mlx, 0x02, reg_data);
    if (status != MLX90393_OK) {
        return status;
    }

//    printf("MLX90393 Ultra Low Power Config: GAIN=%d, RES=3, OSR=0\r\n", MLX90393_GAIN_SEL);

    // 验证设置是否生效
//    uint16_t verify_reg0, verify_reg7, verify_reg8;
//    if (MLX90393_ReadRegister(mlx, 0x00, &verify_reg0) == MLX90393_OK) {
//        uint8_t actual_gain = (verify_reg0 >> 5) & 0x7;
//        printf("Verified GAIN_SEL: %d (reg0=0x%04X)\r\n", actual_gain, verify_reg0);
//    }

//    if (MLX90393_ReadRegister(mlx, 0x07, &verify_reg7) == MLX90393_OK) {
////        printf("Verified XY threshold: %d\r\n", verify_reg7);
//    }

//    if (MLX90393_ReadRegister(mlx, 0x08, &verify_reg8) == MLX90393_OK) {
////        printf("Verified Z threshold: %d\r\n", verify_reg8);
//    }

    return MLX90393_OK;
}



/**
  * @brief  调试原始数据读取函数
  * @param  mlx: MLX90393句柄指针
  * @retval 无
  */
void MLX90393_DebugRawData(MLX90393_Handle_t *mlx)
{
    HAL_StatusTypeDef hal_result;
    uint8_t tx_data[2];
    uint8_t rx_data[9];

    printf("\r\n=== Raw Data Debug ===\r\n");

    // 测试不同的轴选择组合
    uint8_t test_cases[] = {
        MLX90393_AXIS_X,                    // 只测X轴
        MLX90393_AXIS_Y,                    // 只测Y轴
        MLX90393_AXIS_Z,                    // 只测Z轴
        MLX90393_AXIS_X | MLX90393_AXIS_Y,  // 测XY轴
        MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z  // 测XYZ轴
    };

    const char* test_names[] = {
        "X only", "Y only", "Z only", "XY", "XYZ"
    };

    for (int i = 0; i < 5; i++) {
        uint8_t axes = test_cases[i];
        printf("\n--- Test: %s (axes=0x%02X) ---\r\n", test_names[i], axes);

        // 发送SM命令
        tx_data[0] = 0x30 | axes;
        hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("SM command failed\r\n");
            continue;
        }

        // 读取状态
        hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("Status read failed\r\n");
            continue;
        }
        printf("SM Status: 0x%02X\r\n", rx_data[0]);

        HAL_Delay(50);  // 等待转换完成

        // 发送RM命令
        tx_data[0] = 0x40 | axes;
        hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("RM command failed\r\n");
            continue;
        }

        // 计算预期数据长度
        int data_bytes = 0;
        if (axes & MLX90393_AXIS_T) data_bytes += 2;
        if (axes & MLX90393_AXIS_X) data_bytes += 2;
        if (axes & MLX90393_AXIS_Y) data_bytes += 2;
        if (axes & MLX90393_AXIS_Z) data_bytes += 2;

        // 读取数据
        hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, data_bytes + 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("Data read failed\r\n");
            continue;
        }

        printf("RM Status: 0x%02X\r\n", rx_data[0]);
        printf("Raw bytes (%d): ", data_bytes);
        for (int j = 1; j <= data_bytes; j++) {
            printf("0x%02X ", rx_data[j]);
        }
        printf("\r\n");

        HAL_Delay(100);
    }

    printf("=== Debug Complete ===\r\n\r\n");
}

/**
  * @brief  MLX90393修复版本测试函数
  */
void MLX90393_TestFixed(void)
{
    MLX90393_Data_t sensor_data;
    uint8_t result;
    uint32_t test_count = 0;

    printf("\r\n=== MLX90393 Fixed Version Test ===\r\n");

    // 初始化传感器
    result = MLX90393_Init(&mlx_handle, &hi2c1);
    if (result != 0) {
        printf("Sensor initialization failed!\r\n");
        return;
    }
    printf("Sensor initialized successfully\r\n");

    // 运行调试函数
//    printf("\r\n--- Running Raw Data Debug ---\r\n");
//    MLX90393_DebugRawData(&mlx_handle);

    // 连续测量
    printf("\r\n--- Starting Continuous Measurement ---\r\n");
    printf("Using fixed measurement function...\r\n");

    while (test_count < 50) {  // 测试50次
        result = MLX90393_ReadSingleMeasurement_Fixed(&mlx_handle, &sensor_data);

        if (result == 0) {
            test_count++;
            printf("[%03d] X:%7.1f Y:%7.1f Z:%7.1f uT\r\n",
                   test_count, sensor_data.x, sensor_data.y, sensor_data.z);

            // LED指示
            if (test_count % 10 == 0) {
                LED_TOGGLE;
            }
        } else {
            printf("Measurement failed!\r\n");
        }

        HAL_Delay(200);  // 200ms间隔
    }

    printf("=== Test Complete ===\r\n");
}

/**
 * @brief 检查WOC状态和中断原因
 * @param mlx MLX90393句柄
 * @retval 状态码
 */
uint8_t MLX90393_CheckWOCStatus(MLX90393_Handle_t *mlx) {
    uint8_t status_byte;
    static MLX90393_Data_t last_data = {0, 0, 0, 0};
    static uint8_t first_read = 1;

    // 读取状态寄存器
    status_byte = MLX90393_GetStatus(mlx);

    // 如果WOC数据就绪，说明检测到磁场变化
    if (status_byte & 0x10) {
        printf("*** MAGNETIC FIELD CHANGE DETECTED! ***\r\n");

        // 读取当前数据并清除中断 - 只关注Y和Z轴
        MLX90393_Data_t woc_data;
        if (MLX90393_ReadSingleMeasurement(mlx, &woc_data) == MLX90393_OK) {
            printf("Y:%6.1f Z:%5.1f [WOC]\r\n", woc_data.y, woc_data.z);

            // 分析Y和Z轴变化
            if (!first_read) {
                float dy = woc_data.y - last_data.y;
                float dz = woc_data.z - last_data.z;

                printf("Changes: dY=%.1f dZ=%.1f\r\n", dy, dz);

                // 判断主要变化轴 (只考虑Y和Z)
                float abs_dy = dy > 0 ? dy : -dy;
                float abs_dz = dz > 0 ? dz : -dz;

                if (abs_dy > abs_dz) {
                    printf("Primary axis: Y-axis\r\n");
                } else {
                    printf("Primary axis: Z-axis\r\n");
                }
            }

            last_data = woc_data;
            first_read = 0;
        }
        return 1;  // 检测到变化
    }

    return 0;  // 无变化
}

