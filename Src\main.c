/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "mlx90393.h"

// 外部变量声明
extern volatile uint8_t uart_rx_buffer[120];
extern volatile uint16_t uart_rx_index;
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
// 测试模式开关：1=启用测试代码，0=启用状态机代码，2=状态机+嵌入测试
#define TEST_MODE_ENABLED 0
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// MLX90393相关变量
static MLX90393_Handle_t mlx_handle;
volatile uint8_t mlx90393_interrupt_flag = 0;  // MLX90393中断标志位

// 业务流程控制变量
static uint8_t e70_first_init_done = 0;  // E70首次初始化标志
static uint32_t e70_listen_start_time = 0;  // E70监听开始时间
static uint32_t e70_send_start_time = 0;    // E70发送开始时间
static uint8_t e70_send_retry_count = 0;    // E70发送重试次数
static uint16_t saved_battery_voltage = 0;  // 保存的电池电压值

// 业务流程状态枚举
typedef enum {
    STATE_WAKE_UP = 0,           // 唤醒阶段
    STATE_VOLTAGE_CHECK,         // 电压检测阶段
    STATE_E70_LISTEN,           // E70监听阶段
    STATE_E70_SEND_DATA,        // E70发送数据阶段
    STATE_E70_WAIT_ACK,         // E70等待确认阶段
    STATE_PREPARE_SLEEP         // 准备休眠阶段
} BusinessState_t;

static BusinessState_t current_state = STATE_WAKE_UP;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
uint8_t txmp[6]={0xC0,0x00,0x00,0x18,0x04,0x1C};
uint8_t rxmp[10];
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== STM32L031G6 Low Power System ===\r\n");
	LED_ON;

	// E70首次初始化（生命周期内只执行一次）
	if (!e70_first_init_done) {
//		printf("=== E70 First Time Initialization ===\r\n");
		uint8_t init_result = E70_InitializeConfig(E70_MODULE_ADDRESS, 10, 1);
		if (!init_result) {
			printf("E70 Init FAILED!\r\n");
			Error_Handler();
		}
		E70_EnterCommMode();
//		printf("=== E70 Configuration Complete ===\r\n");
		e70_first_init_done = 1;  // 标记首次初始化完成
	}

	// 初始化时关闭所有模块电源
	MLX90393_PW_OFF;
	RF_PWR_OFF;   // 关闭通信模块
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

#if TEST_MODE_ENABLED
    // ==================== 子设备测试代码 ====================

    static uint32_t send_time = 0;
    static uint8_t device_state = 0;  // 0=等待触发, 1=已发送等待确认
    static uint16_t test_device_id = MCU_DEVICE_ID;  // 测试用设备ID，每次发送+1
    static uint16_t test_voltage = 3300;             // 测试用电压，每次发送+1
    uint32_t current_time = HAL_GetTick();

    // 初始化时启动E70通信模式和连续接收
    static uint8_t e70_initialized = 0;
    if (!e70_initialized) {
        E70_EnterCommMode();
        E70_StartContinuousRx();
        e70_initialized = 1;
        printf("Waiting for trigger signal (FC FD 55 66 FD)...\r\n");
    }

    // 检查是否收到数据
    if (uart_rx_index == 5) {
        printf("RX: ");
        for (int i = 0; i < 5; i++) {
            printf("%02X ", uart_rx_buffer[i]);
        }
        printf("\r\n");

        // 检查收到的数据类型
        if (uart_rx_buffer[0] == 0xFC && uart_rx_buffer[1] == 0xFD &&
            uart_rx_buffer[2] == 0x55 && uart_rx_buffer[3] == 0x66 &&
            uart_rx_buffer[4] == 0xFD) {
            // 收到触发信号，发送设备信息
            printf("Trigger received! Sending device info...\r\n");
            E70_SendDeviceInfo(test_device_id, test_voltage);
            test_device_id++;  // 设备ID+1
            test_voltage++;    // 电压+1
            send_time = current_time;
            device_state = 1;  // 进入等待确认状态
        }
        else if (uart_rx_buffer[0] == 0xFC && uart_rx_buffer[1] == 0xFD &&
                 uart_rx_buffer[2] == 0x77 && uart_rx_buffer[3] == 0x88 &&
                 uart_rx_buffer[4] == 0xFD) {
            // 收到确认信号
            printf("Confirmation received! Communication complete.\r\n");
            device_state = 0;  // 回到等待触发状态
        }

        // 清空接收标志，准备接收下一个数据包
        uart_rx_index = 0;
    }

    // 如果处于等待确认状态，检查超时重发（1秒超时，0.5秒间隔重发）
    if (device_state == 1 && (current_time - send_time >= 1000)) {
        printf("Timeout! Resending device info...\r\n");
        E70_SendDeviceInfo(test_device_id, test_voltage);
        test_device_id++;  // 设备ID+1
        test_voltage++;    // 电压+1
        send_time = current_time - 500;  // 设置为0.5秒后再次重发
    }

    HAL_Delay(10); // 主循环延时

#else
    // ==================== 精确业务流程状态机 ====================
    // ==================== 精确业务流程状态机 ====================

    switch (current_state) {

        case STATE_WAKE_UP:
        {
            printf("\r\n=== Device Wake Up ===\r\n");

            // 检查是否是MLX90393中断唤醒，如果是则读取数据清除中断状态
            if (mlx90393_interrupt_flag) {
                mlx90393_interrupt_flag = 0;  // 清除标志位

                MLX90393_Data_t interrupt_data;
                if (MLX90393_ReadSingleMeasurement(&mlx_handle, &interrupt_data) == MLX90393_OK) {
                    printf("MLX90393 Interrupt Data: X=%.1f, Y=%.1f, Z=%.1f\r\n",
                           interrupt_data.x, interrupt_data.y, interrupt_data.z);
                }

                // 读取状态寄存器确认中断已清除
                uint8_t status = MLX90393_GetStatus(&mlx_handle);
            }

            // 立即关闭MLX90393模块电源
            MLX90393_PW_OFF;
            printf("MLX90393 Power: OFF\r\n");

            // 转到电压检测阶段
            current_state = STATE_VOLTAGE_CHECK;
            break;
        }

        case STATE_VOLTAGE_CHECK:
        {
            printf("=== Voltage Check Phase ===\r\n");

            // 等待1秒钟
            HAL_Delay(1000);

            // 读取电池电压值并保存备用
            saved_battery_voltage = ADC_ReadBatteryVoltage();
            float battery_voltage_v = saved_battery_voltage / 1000.0f;
            printf("Battery Voltage: %.3fV (%dmV) - Saved\r\n", battery_voltage_v, saved_battery_voltage);

            // 转到E70通信阶段
            current_state = STATE_E70_LISTEN;
            break;
        }

        case STATE_E70_LISTEN:
        {
            printf("=== E70 Communication Phase ===\r\n");

            // 关键修复：每次唤醒后都要正确设置E70为通信模式(100)
            // 因为休眠时M0、M1、M2引脚被配置为输入模式，唤醒后需要重新设置
            printf("Setting E70 to Communication Mode (100)...\r\n");
            E70_EnterCommMode();  // 这会正确设置M0=0, M1=0, M2=1，然后上电

            // 关键修复：确保UART中断接收正确启动
            HAL_Delay(100);  // 等待E70模式切换完成
            E70_StartContinuousRx();
            printf("E70 Ready for Communication\r\n");

            // 开始监听hlpuart1 RX端口5秒钟
            e70_listen_start_time = HAL_GetTick();
            printf("Listening for 5 seconds...\r\n");

            // 持续监听5秒钟
            while ((HAL_GetTick() - e70_listen_start_time) < 5000) {
                // 修复：检查是否收到了足够的数据（至少5字节）
                if (uart_rx_index >= 5) {
                    // 打印收到的数据用于调试
                    printf("RX (%d bytes): ", uart_rx_index);
                    for (int i = 0; i < uart_rx_index; i++) {
                        printf("%02X ", uart_rx_buffer[i]);
                    }
                    printf("\r\n");

                    // 检查收到的数据是否是ACK
                    if (E70_CheckReceiveAck()) {
                        printf("Valid data received! Proceeding to send device info.\r\n");
                        current_state = STATE_E70_SEND_DATA;
                        e70_send_retry_count = 0;
                        uart_rx_index = 0; // 清除标志
                        E70_ClearRxBuffer();
                        goto state_machine_continue;
                    } else {
                        // 收到了数据但不是有效ACK，清除它并继续监听
                        uart_rx_index = 0;
                        E70_ClearRxBuffer();
                        E70_StartContinuousRx();  // 重新启动接收
                        printf("Received data but not valid ACK, discarding.\r\n");
                    }
                }
                HAL_Delay(50);  // 每50ms检查一次
            }

            // 5秒内未收到数据，关闭E70模块电源，重新初始化MLX90393模块
            printf("No data received in 5 seconds. Shutting down E70.\r\n");
            RF_PWR_OFF;
            current_state = STATE_PREPARE_SLEEP;
            break;
        }


        case STATE_E70_SEND_DATA:
        {
            printf("=== E70 Send Data Phase ===\r\n");

            // 清空接收缓冲区，准备接收新的确认数据
            E70_ClearRxBuffer();

            // 调用E70_SendDeviceInfo()发送设备信息
            if (E70_SendDeviceInfo(MCU_DEVICE_ID, saved_battery_voltage)) {
                printf("Device info sent successfully\r\n");
                printf("Waiting for confirm (FC FD 77 88 FD)...\r\n");
                e70_send_start_time = HAL_GetTick();
                current_state = STATE_E70_WAIT_ACK;
            } else {
                printf("Failed to send device info\r\n");
                // 发送失败，直接关闭E70模块
                RF_PWR_OFF;
                current_state = STATE_PREPARE_SLEEP;
            }
            break;
        }

        case STATE_E70_WAIT_ACK:
        {
            // 持续检查hlpuart1 RX是否收到确认回复 (FC FD + 77 88 + FD)
            if (E70_CheckReceiveConfirm()) {
                printf("Confirm received! Data transmission successful.\r\n");
                // 成功发送，关闭E70模块电源
                RF_PWR_OFF;
                current_state = STATE_PREPARE_SLEEP;
            } else if ((HAL_GetTick() - e70_send_start_time) > 1000) {
                // 超过2秒未收到确认数据
                e70_send_retry_count++;
                printf("Confirm timeout. Retry count: %d\r\n", e70_send_retry_count);

                if (e70_send_retry_count < 5) {
                    // 重新发送
                    printf("Retrying data transmission...\r\n");
                    current_state = STATE_E70_SEND_DATA;
                } else {
                    // 最多重试5次，仍无响应则关闭E70模块电源
                    printf("Max retries reached. Shutting down E70.\r\n");
                    RF_PWR_OFF;
                    current_state = STATE_PREPARE_SLEEP;
                }
            }
            // 继续等待确认或超时
            HAL_Delay(100);
            break;
        }

        case STATE_PREPARE_SLEEP:
        {
            printf("=== Prepare Sleep Phase ===\r\n");

//            // 重新上电并初始化MLX90393模块
            MLX90393_PW_ON;
            HAL_Delay(100); // 等待上电稳定

            if (MLX90393_Init(&mlx_handle, &hi2c1) == MLX90393_OK) {
                printf("MLX90393 Re-Init: SUCCESS\r\n");

                // 首先配置超低功耗模式
                if (MLX90393_EnterLowPowerMode(&mlx_handle) == MLX90393_OK) {
                    printf("MLX90393 Low Power Mode: ENABLED\r\n");
                }

                // 然后配置WOC模式（唤醒变化模式）
                if (MLX90393_ConfigureWOC(&mlx_handle) == MLX90393_OK) {
                    printf("MLX90393 WOC Mode: ENABLED\r\n");

                    // 关键：读取一次数据让MLX90393进入正常工作状态
                    MLX90393_Data_t dummy_data;
                    HAL_Delay(100);  // 等待WOC模式稳定

                    if (MLX90393_ReadSingleMeasurement(&mlx_handle, &dummy_data) == MLX90393_OK) {
                        printf("MLX90393 Initial Read: X=%.1f, Y=%.1f, Z=%.1f\r\n",
                               dummy_data.x, dummy_data.y, dummy_data.z);
                    }
                }
            } else {
                printf("MLX90393 Re-Init: FAILED\r\n");
            }

            // 准备进入休眠模式，重置状态机
            current_state = STATE_WAKE_UP;
            printf("Entering sleep mode...\r\n");

            // 继续执行原有的休眠逻辑
            goto enter_sleep_mode;
        }
    }

    state_machine_continue:
    // 状态机继续执行，不进入休眠
    continue;

    enter_sleep_mode:
    // 进入休眠模式处理
    {
        // 声明GPIO配置结构体，用于休眠配置
        GPIO_InitTypeDef GPIO_InitStruct = {0};

    // ==================== 进入休眠模式 ====================
    printf("Preparing to enter STOP mode...\r\n");
    printf("Device will wake up on MLX90393 interrupt (PB0)\r\n");
    HAL_Delay(500); // 让串口输出完成

    // 准备进入低功耗模式 - 采用FreeRTOS示例的彻底方法

    // 关闭所有外部模块电源
    RF_PWR_OFF;   // 关闭通信模块
    LED_OFF;      //关闭LED
    // 停止并禁用UART中断
    HAL_UART_AbortReceive_IT(&huart2);
    HAL_UART_AbortReceive_IT(&hlpuart1);

    // 完全停止ADC和DMA
    HAL_ADC_Stop_DMA(&hadc);
    HAL_ADC_Stop(&hadc);
    if(hadc.DMA_Handle != NULL) {
        HAL_DMA_Abort(hadc.DMA_Handle);
    }

    // 停止I2C传输
    HAL_I2C_Master_Abort_IT(&hi2c1, MLX90393_I2C_ADDR_SHIFTED);

    // 特殊处理：配置通信模块相关引脚为低功耗状态

    // 将M0、M1、M2配置为输入模式（避免输出驱动消耗功耗）
    GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 将LPUART1引脚配置为输入模式
    GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;  // PA2(TX), PA3(RX)
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;  // 下拉到低电平
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 将I2C引脚配置为输入模式
    GPIO_InitStruct.Pin = GPIO_PIN_9 | GPIO_PIN_10;  // PB9(SCL), PB10(SDA)
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

//    printf("Communication module pins configured for low power\r\n");

    // 关闭外设时钟
    __HAL_RCC_USART2_CLK_DISABLE();
    __HAL_RCC_LPUART1_CLK_DISABLE();
    __HAL_RCC_ADC1_CLK_DISABLE();
    __HAL_RCC_DMA1_CLK_DISABLE();
    __HAL_RCC_I2C1_CLK_DISABLE();

    // 确保UART传输完成
    while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
    while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
    HAL_Delay(10);

    // 等待串口输出完成
    while(__HAL_UART_GET_FLAG(&huart2, UART_FLAG_TC) == RESET);
    HAL_Delay(50);

    // 8. 彻底的低功耗模式进入（参考FreeRTOS示例）

    // 禁用所有中断，准备进入低功耗模式
    __disable_irq();

    // 禁用SysTick中断
    SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

    // 禁用所有中断，只保留EXTI0_1唤醒中断
    for (uint8_t i = 0; i < 8; i++) {
        NVIC->ICER[i] = 0xFFFFFFFF;
    }

    // 确保EXTI0_1中断被启用（PB0唤醒中断）
    HAL_NVIC_SetPriority(EXTI0_1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(EXTI0_1_IRQn);

    // 清除所有挂起的中断
    for (uint8_t i = 0; i < 8; i++) {
        NVIC->ICPR[i] = 0xFFFFFFFF;
    }

    // 清除PWR标志
    __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);

    // 重新启用全局中断，但只有EXTI0_1中断处于活动状态
    __enable_irq();

    // 进入STOP模式，使用低功耗调节器
    HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

    // ==================== 唤醒后恢复（参考FreeRTOS示例）====================

    // 打开LED表示唤醒开始
    LED_ON;

    // 重新配置系统时钟 - STOP模式后必需
    SystemClock_Config();

    // 重新启用SysTick中断
    SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

    // 重新启用必要的外设时钟
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_LPUART1_CLK_ENABLE();
    __HAL_RCC_ADC1_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();
    __HAL_RCC_I2C1_CLK_ENABLE();

    // 重新配置GPIO（恢复正常功能）
    MX_GPIO_Init();

    // 重新初始化外设
    MX_USART2_UART_Init();
    MX_LPUART1_UART_Init();
    MX_ADC_Init();
    MX_I2C1_Init();

    // 重新启用必要的中断
    HAL_NVIC_SetPriority(USART2_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(USART2_IRQn);
    HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(LPUART1_IRQn);

    // 确保UART完全重新初始化
    HAL_Delay(100);  // 增加延时确保UART稳定

    // 关键修复：重新初始化E70接收状态
    E70_ResetRxState();

    // 修复：测试UART是否工作正常
    printf("UART recovery test after wake up\r\n");

    // 注意：不在这里启动接收，等到状态机中E70_EnterCommMode()后再启动


    // 特殊恢复：重新配置通信模块引脚为正常工作状态
    // 复用之前声明的GPIO_InitStruct变量

    // 恢复M0、M1、M2为输出模式
    GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 设置通信模式：M0=0, M1=0, M2=1 (100模式)
    HAL_GPIO_WritePin(GPIOA, M0_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOA, M1_Pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GPIOA, M2_Pin, GPIO_PIN_SET);

    printf("Communication module pins restored\r\n");

    // LED闪烁表示正在唤醒
    LED_TOGGLE;
    HAL_Delay(100);
    LED_TOGGLE;
		HAL_Delay(100);
    LED_TOGGLE;
		HAL_Delay(100);
    LED_ON;

    printf("\r\n*** WAKE UP from STOP mode ***\r\n");

    } // 结束 enter_sleep_mode 代码块

#endif // TEST_MODE_ENABLED

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_SYSCLK;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
