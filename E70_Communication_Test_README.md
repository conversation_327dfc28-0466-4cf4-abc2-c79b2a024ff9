# E70通信测试说明

## 测试目的
验证两个E70设备之间的基本通信功能是否正常工作。

## 测试原理
- **发送端设备**：固定发送 `FC FD 77 88 FD`，期望收到 `FC FD 55 66 FD`
- **接收端设备**：固定发送 `FC FD 55 66 FD`，期望收到 `FC FD 77 88 FD`

## 如何配置测试模式

### 方法1：修改代码（推荐）
在 `main.c` 文件的初始化部分找到以下代码：
```c
// 设置测试模式（可以通过修改这个值来切换发送端/接收端）
// test_mode = 1: 发送端（发送 FC FD 77 88 FD，期望收到 FC FD 55 66 FD）
// test_mode = 0: 接收端（发送 FC FD 55 66 FD，期望收到 FC FD 77 88 FD）
test_mode = 1;  // 默认为发送端，另一个设备设置为0
```

- **设备A**：设置 `test_mode = 1;` （发送端）
- **设备B**：设置 `test_mode = 0;` （接收端）

## 测试步骤

1. **准备两个设备**
   - 设备A：编译并烧录发送端代码（test_mode = 1）
   - 设备B：编译并烧录接收端代码（test_mode = 0）

2. **同时上电启动**
   - 两个设备同时上电
   - 观察串口调试输出

3. **观察测试结果**
   - 每个设备每3秒发送一次测试数据
   - 每个设备每500ms检查一次接收数据
   - 当收到期望的数据包时，会显示：
     ```
     *** RECEIVED EXPECTED: FC FD XX XX FD ***
     *** COMMUNICATION TEST PASSED! ***
     ```

## 预期输出示例

### 发送端设备（test_mode = 1）输出：
```
=== STM32L031G6 Low Power System ===
E70 Initialize Start
Config OK
Current Config: Addr=0x6666, Ch=10, Pwr=10dBm
Entering Comm Mode
Comm Mode Ready

=== E70 Communication Test Ready ===
Test Mode: SENDER

=== E70 Communication Test Start ===
Test Mode: SENDER (Send FC FD 77 88 FD)
Sending: FC FD 77 88 FD
*** RECEIVED EXPECTED: FC FD 55 66 FD ***
*** COMMUNICATION TEST PASSED! ***
```

### 接收端设备（test_mode = 0）输出：
```
=== STM32L031G6 Low Power System ===
E70 Initialize Start
Config OK
Current Config: Addr=0x6666, Ch=10, Pwr=10dBm
Entering Comm Mode
Comm Mode Ready

=== E70 Communication Test Ready ===
Test Mode: RECEIVER

=== E70 Communication Test Start ===
Test Mode: RECEIVER (Send FC FD 55 66 FD)
Sending: FC FD 55 66 FD
*** RECEIVED EXPECTED: FC FD 77 88 FD ***
*** COMMUNICATION TEST PASSED! ***
```

## 故障排除

### 新增调试功能：
1. **硬件诊断**：检查E70模式引脚、电源状态、UART引脚状态
2. **回环测试**：启动时自动执行，测试E70模块基本收发功能
3. **直接UART测试**：绕过E70模块，直接测试UART硬件
4. **透传模式验证**：确认E70是否真的进入透传模式
5. **接收状态调试**：每5秒显示接收缓冲区状态
6. **UART状态检查**：每5秒检查UART接收状态并自动恢复

### 如果回环测试失败：
```
*** LOOPBACK TEST FAILED - NO DATA RECEIVED ***
This indicates UART RX or E70 module issue
```
**解决方案：**
1. 检查E70模块电源连接
2. 检查UART引脚连接（TX/RX是否正确）
3. 检查E70模块是否正确进入通信模式
4. 检查M0/M1/M2引脚设置

### 如果没有收到期望数据：
**查看调试输出：**
```
RX Debug: Buffer index=0
No data received yet
UART Status Check:
- UART State: 1
- RX State: 2
- Last RX: 15000 ms ago
```

**解决方案：**
1. 检查两个设备的E70模块是否都正常初始化
2. 检查两个设备是否使用相同的信道和地址配置
3. 检查设备之间的距离是否合适（建议1-10米测试）
4. 检查天线连接是否正常
5. 如果UART RX State不是2（BUSY_RX），系统会自动重启接收

### 如果收到错误数据：
- 串口会显示实际收到的数据，例如：
  ```
  Found packet at pos 0: FC FD 12 34 FD
  Received: FC FD 12 34 FD (Expected: 55 66)
  ```

### 如果完全没有数据：

#### 步骤1：检查硬件诊断输出
```
=== E70 Hardware Diagnosis ===
E70 Mode Pins:
- M0 (should be 1): 1
- M1 (should be 0): 0
- M2 (should be 0): 0
Mode = 001 (should be 001 for transparent mode)
Power Control:
- RF_PWR: 1 (should be 1)
```
**如果模式引脚不正确**：检查M0/M1/M2的硬件连接

#### 步骤2：检查透传模式验证
```
Comm Mode Ready - Transparent mode verified
```
**如果显示**：
```
ERROR: E70 responded to config command in transparent mode!
E70 is NOT in transparent mode!
```
说明E70没有正确进入透传模式，检查模式切换时序

#### 步骤3：检查直接UART测试
```
*** DIRECT UART TEST PASSED! ***
This means UART is working, problem is with E70 module
```
**如果UART测试通过但E70不工作**：
- E70模块硬件故障
- E70模块没有正确连接到UART
- E70模块电源问题

**如果UART测试也失败**：
- UART硬件配置问题
- PA2/PA3引脚连接问题
- 时钟配置问题

#### 步骤4：常见问题解决
1. **E70模块电源**：确保RF_PWR引脚输出高电平
2. **UART连接**：确认PA2连接到E70的RXD，PA3连接到E70的TXD
3. **模式引脚**：确认M0=1, M1=0, M2=0（透传模式）
4. **上电时序**：确保先设置模式引脚，再给E70上电
5. **天线连接**：确保天线正确连接到E70模块

## 测试完成后
测试成功后，可以：
1. 停止测试：设置 `test_running = 0;`
2. 恢复正常业务逻辑
3. 根据测试结果调整通信参数

## 注意事项
- 测试期间E70模块会保持开启状态
- 测试会持续运行直到手动停止
- 建议在空旷环境下进行测试以获得最佳效果
