/**
 * @file    e70_config.c
 * @brief   E70 LoRa模块配置实现 - 实际验证可用版本
 * <AUTHOR> Name
 * @date    2025年1月
 * @note    此版本已通过实际硬件测试验证，所有基本功能正常工作
 *
 * 验证结果摘要：
 * ✅ 配置模式5 (101): 完全正常，可读写配置
 * ✅ C1 C1 C1 命令: 成功读取6字节配置数据
 * ✅ C0 写配置命令: 成功写入并立即生效
 * ✅ 模式切换: 断电→设置引脚→上电时序正确
 * ❌ 配置模式3 (011): 无响应，不可用
 * ❌ C3 C3 C3 命令: 此型号不支持版本读取
 *
 * 关键函数：
 * - E70_SetModeWithPowerCycle(): 唯一正确的模式切换方法
 * - E70_WriteSimpleConfig(): 已验证的配置写入函数
 * - HAL_UART_RxCpltCallback(): 简化的中断接收，无打印
 */

#include "e70_config.h"
#include "gpio.h"
#include <string.h>
#include <stdio.h>

// 中断接收缓存
volatile uint8_t uart_rx_buffer[120];
volatile uint16_t uart_rx_index = 0;
volatile uint32_t last_rx_tick = 0;

// 私有函数声明
static HAL_StatusTypeDef E70_SendCommand(uint8_t *cmd, uint8_t cmd_len, uint8_t *response, uint8_t *resp_len, uint32_t timeout);

// 串口重定向支持printf
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart2, (uint8_t *)&ch, 1, 0xFFFF);
    return ch;
}


/**
 * @brief  初始化E70模块
 * @param  None
 * @retval None
 */
void E70_Init(void)
{
    // GPIO 已在 MX_GPIO_Init() 中完成配置；此处不再重复初始化
    // 模式切换统一使用 E70_SetModeWithPowerCycle()，不在此函数内切换模式
}

/*
 * 注意：E70 模式在上电时锁存，禁止运行时调用 E70_SetMode。请使用 E70_SetModeWithPowerCycle。
 */

/**
 * @brief  设置E70模式并重新上电 (正确的方法)
 * @param  mode: 工作模式
 * @retval None
 */
void E70_SetModeWithPowerCycle(E70_Mode_t mode)
{
    // 1. 断电
    RF_PWR_OFF;
    HAL_Delay(500);

    // 2. 设置模式引脚
    HAL_GPIO_WritePin(M0_GPIO_Port, M0_Pin, (mode & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(M1_GPIO_Port, M1_Pin, (mode & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(M2_GPIO_Port, M2_Pin, (mode & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);

    HAL_Delay(200);  // 让引脚状态稳定

    // 3. 上电
    RF_PWR_ON;
    HAL_Delay(500);  // 等待启动完成
}



/**
 * @brief  发送命令并接收响应
 * @param  cmd: 命令数据
 * @param  cmd_len: 命令长度
 * @param  response: 响应缓冲区
 * @param  resp_len: 响应长度指针
 * @param  timeout: 超时时间
 * @retval HAL_StatusTypeDef
 */
static HAL_StatusTypeDef E70_SendCommand(uint8_t *cmd, uint8_t cmd_len, uint8_t *response, uint8_t *resp_len, uint32_t timeout)
{
    HAL_StatusTypeDef status;

    // 发送命令
    status = HAL_UART_Transmit(&hlpuart1, cmd, cmd_len, timeout);
    if (status != HAL_OK) {
        return status;
    }

    // 接收响应
    status = HAL_UART_Receive(&hlpuart1, response, *resp_len, timeout);
    if (status == HAL_TIMEOUT) {
        *resp_len = 0;
    }

    return status;
}

/**
 * @brief  读取模块配置
 * @param  config: 配置结构体指针
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_ReadConfig(E70_Config_t *config)
{
    uint8_t cmd[3] = {0xC1, 0xC1, 0xC1};
    uint8_t response[10];  // 增加缓冲区大小
    uint8_t resp_len = 10;
    HAL_StatusTypeDef status;

    status = E70_SendCommand(cmd, sizeof(cmd), response, &resp_len, E70_CONFIG_TIMEOUT);

    if (status == HAL_OK && resp_len > 0) {
        // 打印调试信息
        printf("Config response (%d bytes): ", resp_len);
        for (int i = 0; i < resp_len; i++) {
            printf("0x%02X ", response[i]);
        }
        printf("\r\n");

        // 查找C0标识符
        int config_start = -1;
        for (int i = 0; i < resp_len; i++) {
            if (response[i] == 0xC0) {
                config_start = i;
                break;
            }
        }

        if (config_start >= 0 && (config_start + 5) < resp_len) {
            config->head = response[config_start];
            config->addh = response[config_start + 1];
            config->addl = response[config_start + 2];
            config->sped = response[config_start + 3];
            config->chan = response[config_start + 4];
            config->option = response[config_start + 5];
            printf("Config parsed: ADDH=0x%02X, ADDL=0x%02X, CHAN=%d\r\n",
                   config->addh, config->addl, config->chan & 0x1F);
            return HAL_OK;
        }
    }

    printf("Config read failed or invalid response\r\n");
    return HAL_ERROR;
}

/**
 * @brief  写入模块配置
 * @param  config: 配置结构体指针
 * @param  save: 是否保存到flash (1=保存, 0=临时)
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_WriteConfig(E70_Config_t *config, uint8_t save)
{
    uint8_t cmd[6];
    uint8_t response[6];
    uint8_t resp_len = 6;

    // 构造写入命令
    cmd[0] = save ? 0xC0 : 0xC2;  // C0=保存到flash, C2=临时设置
    cmd[1] = config->addh;
    cmd[2] = config->addl;
    cmd[3] = config->sped;
    cmd[4] = config->chan;
    cmd[5] = config->option;

    return E70_SendCommand(cmd, sizeof(cmd), response, &resp_len, E70_CONFIG_TIMEOUT);
}

/**
 * @brief  设置模块地址 (假设已经在配置模式)
 * @param  address: 16位地址 (0x0000-0xFFFF)
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_SetAddress(uint16_t address)
{
    HAL_StatusTypeDef status;
    E70_Config_t config;

    // 读取当前配置
    status = E70_ReadConfig(&config);
    if (status != HAL_OK) {
        return status;
    }

    // 修改地址
    config.addh = (address >> 8) & 0xFF;
    config.addl = address & 0xFF;

    // 写入配置
    status = E70_WriteConfig(&config, 1); // 保存到flash

    return status;
}

/**
 * @brief  设置模块信道 (假设已经在配置模式)
 * @param  channel: 信道号 (0-31)
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_SetChannel(uint8_t channel)
{
    if (channel > 31) {
        return HAL_ERROR;
    }

    HAL_StatusTypeDef status;
    E70_Config_t config;

    // 读取当前配置
    status = E70_ReadConfig(&config);
    if (status != HAL_OK) {
        return status;
    }

    // 修改信道，保持包长设置不变
    config.chan = (config.chan & 0xE0) | (channel & 0x1F);

    // 写入配置
    status = E70_WriteConfig(&config, 1);

    return status;
}

/**
 * @brief  设置发射功率 (假设已经在配置模式)
 * @param  power: 发射功率
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_SetTxPower(E70_Power_t power)
{
    HAL_StatusTypeDef status;
    E70_Config_t config;

    // 读取当前配置
    status = E70_ReadConfig(&config);
    if (status != HAL_OK) {
        return status;
    }

    // 修改发射功率，保持其他选项不变
    config.option = (config.option & 0xFC) | (power & 0x03);

    // 写入配置
    status = E70_WriteConfig(&config, 1);

    return status;
}

/**
 * @brief  获取模块版本信息 (假设已经在配置模式)
 * @param  version: 版本信息缓冲区
 * @param  len: 缓冲区长度
 * @retval HAL_StatusTypeDef
 */
HAL_StatusTypeDef E70_GetVersion(uint8_t *version, uint8_t len)
{
    uint8_t cmd[3] = {0xC3, 0xC3, 0xC3};
    uint8_t response[10];
    uint8_t resp_len = 10;
    HAL_StatusTypeDef status;

    status = E70_SendCommand(cmd, sizeof(cmd), response, &resp_len, E70_CONFIG_TIMEOUT);

    if (status == HAL_OK && resp_len > 0) {
        printf("Version response (%d bytes): ", resp_len);
        for (int i = 0; i < resp_len; i++) {
            printf("0x%02X ", response[i]);
        }
        printf("\r\n");

        // 复制到用户缓冲区
        int copy_len = (resp_len < len) ? resp_len : len;
        memcpy(version, response, copy_len);

        return HAL_OK;
    }

    printf("Version read failed\r\n");
    return HAL_ERROR;
}

/**
 * @brief  启动中断接收（配置阶段用，逐字节接收）
 * @param  None
 * @retval None
 */
void E70_StartRx(void)
{
    uart_rx_index = 0;
    memset((void*)uart_rx_buffer, 0, sizeof(uart_rx_buffer));
    E70_SetRxMode(0);     // 设置为逐字节接收模式
    HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&uart_rx_buffer[0], 1);
}

/**
 * @brief  停止中断接收
 * @param  None
 * @retval None
 */
void E70_StopRx(void)
{
    HAL_UART_AbortReceive_IT(&hlpuart1);
}

/**
 * @brief  打印接收缓存
 * @param  None
 * @retval None
 */
void E70_PrintRxBuffer(void)
{
    printf("=== RX Buffer Dump ===\r\n");
    printf("Total bytes received: %d\r\n", uart_rx_index);

    if (uart_rx_index > 0) {
        printf("Data: ");
        for (int i = 0; i < uart_rx_index; i++) {
            printf("0x%02X ", uart_rx_buffer[i]);
        }
        printf("\r\n");
    } else {
        printf("No data received\r\n");
    }
    printf("======================\r\n");
}

/**
 * @brief  解析配置数据
 * @param  None
 * @retval None
 */
void E70_ParseConfigData(void)
{
    if (uart_rx_index >= 6 && uart_rx_buffer[0] == 0xC0) {
        printf("=== Config Data Analysis ===\r\n");
        printf("Header: 0x%02X\r\n", uart_rx_buffer[0]);
        printf("Address: 0x%02X%02X\r\n", uart_rx_buffer[1], uart_rx_buffer[2]);
        printf("SPED: 0x%02X (Baud: %s, Air: %s)\r\n", uart_rx_buffer[3],
               ((uart_rx_buffer[3] >> 3) & 0x07) == 3 ? "9600" : "Other",
               (uart_rx_buffer[3] & 0x07) == 0 ? "2.5k" : "Other");
        printf("Channel: %d\r\n", uart_rx_buffer[4] & 0x1F);
        printf("Option: 0x%02X (Power: %ddBm)\r\n", uart_rx_buffer[5],
               14 - ((uart_rx_buffer[5] & 0x03) * 3));
        printf("============================\r\n");
    } else if (uart_rx_index >= 4 && uart_rx_buffer[0] == 0xC3) {
        printf("=== Version Data Analysis ===\r\n");
        printf("Header: 0x%02X\r\n", uart_rx_buffer[0]);
        printf("Model: E%d series\r\n", uart_rx_buffer[1] == 0x70 ? 70 : uart_rx_buffer[1]);
        printf("Version: ");
        for (int i = 2; i < uart_rx_index; i++) {
            printf("0x%02X ", uart_rx_buffer[i]);
        }
        printf("\r\n============================\r\n");
    }
}

/**
 * @brief  写入简单配置参数
 * @param  address: 模块地址
 * @param  channel: 信道 (0-31)
 * @param  power: 发射功率 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
 * @retval 是否成功
 */
uint8_t E70_WriteSimpleConfig(uint16_t address, uint8_t channel, uint8_t power)
{
    // 构造配置命令: C0 ADDH ADDL SPED CHAN OPTION
    uint8_t cmd[6];
    cmd[0] = 0xC0;                          // 保存到flash
    cmd[1] = (address >> 8) & 0xFF;         // 地址高字节
    cmd[2] = address & 0xFF;                // 地址低字节
    cmd[3] = 0x18;                          // SPED: 9600bps, 2.5k空中速率
    cmd[4] = (channel & 0x1F) | 0x40;       // 信道 + 64字节包长
    cmd[5] = 0x1C | (power & 0x03);         // 选项 + 功率设置

    // 发送配置命令
    E70_StartRx();
    HAL_UART_Transmit(&hlpuart1, cmd, 6, 1000);
    HAL_Delay(1000);
    E70_StopRx();

    // 检查响应
    if (uart_rx_index >= 6 && uart_rx_buffer[0] == 0xC0) {
        return 1;  // 配置成功
    } else {
        return 0;  // 配置失败
    }
}

/**
 * @brief  读取并打印当前配置
 * @param  None
 * @retval None
 */
void E70_ReadAndPrintConfig(void)
{
    // 发送读取配置命令 C1 C1 C1
    E70_StartRx();
    uint8_t cmd[] = {0xC1, 0xC1, 0xC1};
    HAL_UART_Transmit(&hlpuart1, cmd, 3, 1000);
    HAL_Delay(1000);
    E70_StopRx();

    // 检查并解析响应
    if (uart_rx_index >= 6 && uart_rx_buffer[0] == 0xC0) {
        uint16_t address = (uart_rx_buffer[1] << 8) | uart_rx_buffer[2];
        uint8_t channel = uart_rx_buffer[4] & 0x1F;
        uint8_t power_bits = uart_rx_buffer[5] & 0x03;

        // 功率对照表：0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm
        uint8_t power_dbm;
        switch(power_bits) {
            case 0: power_dbm = 14; break;
            case 1: power_dbm = 10; break;
            case 2: power_dbm = 7; break;
            case 3: power_dbm = 4; break;
            default: power_dbm = 14; break;
        }

        printf("Current Config: Addr=0x%04X, Ch=%d, Pwr=%ddBm\r\n",
               address, channel, power_dbm);
    } else {
        printf("Config read failed\r\n");
    }
}

// ==================== 通信功能实现 ====================

/**
 * @brief  初始化E70配置（生命周期内只调用一次）
 * @param  e70_module_address: E70模块网络地址（用于LoRa组网）
 * @param  channel: 信道 (0-31)
 * @param  power: 发射功率 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
 * @retval 是否成功
 */
uint8_t E70_InitializeConfig(uint16_t e70_module_address, uint8_t channel, uint8_t power)
{
    printf("E70 Initialize Start\r\n");

    // 进入配置模式 (101) - M0=1, M1=0, M2=1
    E70_SetModeWithPowerCycle(E70_MODE_CONFIG);
    E70_Init();

    // 写入配置
    uint8_t result = E70_WriteSimpleConfig(e70_module_address, channel, power);
    if (result) {
        printf("Config OK\r\n");
        // 验证配置
        E70_ReadAndPrintConfig();
        return 1;
    } else {
        printf("Config FAILED\r\n");
        return 0;
    }
}

/**
 * @brief  进入连续通信模式
 * @param  None
 * @retval 是否成功
 */
uint8_t E70_EnterCommMode(void)
{
    printf("Entering Comm Mode\r\n");
    E70_SetModeWithPowerCycle(E70_MODE_COMM);  // 使用100模式 (M0=0, M1=0, M2=1)
    printf("Comm Mode Ready\r\n");
    return 1;
}

/**
 * @brief  发送设备信息
 * @param  mcu_device_id: 单片机设备ID（用于业务逻辑识别）
 * @param  battery_voltage: 电池电压
 * @retval 是否成功
 */
uint8_t E70_SendDeviceInfo(uint16_t mcu_device_id, uint16_t battery_voltage)
{
    // 使用字节数组而不是结构体，避免字节对齐问题
    uint8_t packet[E70_SEND_PACKET_SIZE];

    // 构造数据包
    packet[0] = E70_SEND_HEADER1;               // 0xFA
    packet[1] = E70_SEND_HEADER2;               // 0xFB
    // 大端序（高位在前）
    packet[2] = (mcu_device_id >> 8) & 0xFF;    // ID高字节
    packet[3] = mcu_device_id & 0xFF;           // ID低字节
    packet[4] = (battery_voltage >> 8) & 0xFF;  // 电压高字节
    packet[5] = battery_voltage & 0xFF;         // 电压低字节
    packet[6] = E70_PACKET_END;                 // 0xFD

    // 关键修复：不停止接收，直接发送，保持接收缓冲区
    // 这样可以在发送的同时继续接收确认数据

    // 发送数据包
    HAL_StatusTypeDef status = HAL_UART_Transmit(&hlpuart1, packet, E70_SEND_PACKET_SIZE, 1000);

    if (status == HAL_OK) {
        printf("Sent: ID=0x%04X, Volt=%dmV\r\n", mcu_device_id, battery_voltage);

        // 修复：发送完成后短暂延时，但不清空接收缓冲区
        HAL_Delay(10);  // 最小延时，确保发送完成

        return 1;
    } else {
        printf("Send failed with status: %d\r\n", status);
        return 0;
    }
}

/**
 * @brief  检查是否收到阶段3的ACK回复 (FC FD + 55 66 + FD)
 * @param  None
 * @retval 是否收到正确ACK
 */
uint8_t E70_CheckReceiveAck(void)
{
    // 修复：在接收缓冲区中搜索ACK数据包，处理数据偏移问题
    if (uart_rx_index >= 5) {  // 至少需要5字节
        for (int i = 0; i <= uart_rx_index - 5; i++) {
            // 检查数据包格式：FC FD 55 66 FD
            if (uart_rx_buffer[i] == E70_RECV_HEADER1 &&        // 0xFC
                uart_rx_buffer[i+1] == E70_RECV_HEADER2 &&      // 0xFD
                uart_rx_buffer[i+2] == E70_RECV_ACK1 &&         // 0x55
                uart_rx_buffer[i+3] == E70_RECV_ACK2 &&         // 0x66
                uart_rx_buffer[i+4] == E70_PACKET_END) {        // 0xFD

                printf("Stage3 ACK Received (FC FD 55 66 FD) at offset %d\r\n", i);
                return 1;
            }
        }
    }

    // 调试：如果没找到有效ACK，打印详细信息
    printf("ACK check failed. Buffer content: ");
    for (int i = 0; i < uart_rx_index && i < 10; i++) {
        printf("%02X ", uart_rx_buffer[i]);
    }
    printf("(total %d bytes)\r\n", uart_rx_index);

    return 0;
}

/**
 * @brief  检查是否收到阶段5的确认回复 (FC FD + 77 88 + FD)
 * @param  None
 * @retval 是否收到正确确认
 */
uint8_t E70_CheckReceiveConfirm(void)
{
    // 修复：在接收缓冲区中搜索确认数据包，处理数据偏移问题
    if (uart_rx_index >= 5) {  // 至少需要5字节
        for (int i = 0; i <= uart_rx_index - 5; i++) {
            // 检查数据包格式：FC FD 77 88 FD
            if (uart_rx_buffer[i] == E70_RECV_HEADER1 &&        // 0xFC
                uart_rx_buffer[i+1] == E70_RECV_HEADER2 &&      // 0xFD
                uart_rx_buffer[i+2] == E70_RECV_CONFIRM1 &&     // 0x77
                uart_rx_buffer[i+3] == E70_RECV_CONFIRM2 &&     // 0x88
                uart_rx_buffer[i+4] == E70_PACKET_END) {        // 0xFD

                printf("Stage5 Confirm Received (FC FD 77 88 FD) at offset %d\r\n", i);
                return 1;
            }
        }
    }

    // 调试：如果没找到有效确认，打印详细信息
    printf("Confirm check failed. Buffer content: ");
    for (int i = 0; i < uart_rx_index && i < 10; i++) {
        printf("%02X ", uart_rx_buffer[i]);
    }
    printf("(total %d bytes)\r\n", uart_rx_index);

    return 0;
}

/**
 * @brief  清空接收缓冲区
 * @param  None
 * @retval None
 */
void E70_ClearRxBuffer(void)
{
    uart_rx_index = 0;
    memset((void*)uart_rx_buffer, 0, sizeof(uart_rx_buffer));
}

/**
 * @brief  启动连续接收模式（通信阶段用）
 * @param  None
 * @retval None
 */
void E70_StartContinuousRx(void)
{
    // 先停止任何正在进行的接收
    HAL_UART_AbortReceive_IT(&hlpuart1);
    HAL_Delay(10);  // 短暂延时确保停止完成

    E70_ClearRxBuffer();  // 清空缓冲区
    E70_SetRxMode(0);     // 修复：改为逐字节接收模式，避免同步问题

    // 修复：使用逐字节接收，更可靠
    HAL_StatusTypeDef status = HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&uart_rx_buffer[0], 1);
    if (status != HAL_OK) {
        printf("UART Receive IT failed: %d\r\n", status);
    }
}

/**
 * @brief  停止连续接收模式
 * @param  None
 * @retval None
 */
void E70_StopContinuousRx(void)
{
    HAL_UART_AbortReceive_IT(&hlpuart1);
}

/**
 * @brief  重置E70接收状态（用于唤醒后恢复）
 * @param  None
 * @retval None
 */
void E70_ResetRxState(void)
{
    // 停止任何正在进行的接收
    HAL_UART_AbortReceive_IT(&hlpuart1);
    HAL_Delay(10);  // 确保停止完成

    // 清空缓冲区
    E70_ClearRxBuffer();

    // 重置接收模式为逐字节接收模式
    E70_SetRxMode(0);

    printf("E70 RX state reset complete\r\n");
}

/**
 * @brief  测试UART接收功能（调试用）
 * @param  None
 * @retval None
 */
void E70_TestUARTRx(void)
{
    static uint32_t last_rx_index = 0;
    static uint32_t last_check_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_check_time > 1000) {  // 每秒检查一次
        if (uart_rx_index != last_rx_index) {
            printf("RX activity detected: index changed from %d to %d\r\n",
                   last_rx_index, uart_rx_index);
            last_rx_index = uart_rx_index;
        } else {
            printf("No RX activity (index still %d)\r\n", uart_rx_index);
        }
        last_check_time = current_time;
    }
}

/**
 * @brief  检查UART状态（调试用）
 * @param  None
 * @retval None
 */
void E70_CheckUARTStatus(void)
{
    printf("UART Status Check:\r\n");
    printf("- LPUART1 State: %d\r\n", hlpuart1.gState);
    printf("- LPUART1 RxState: %d\r\n", hlpuart1.RxState);
    printf("- RX Mode: %d\r\n", E70_GetRxMode());
    printf("- RX Index: %d\r\n", uart_rx_index);
}

// ==================== ADC电池电压采样功能实现 ====================

/**
 * @brief  读取电池电压ADC值
 * @param  None
 * @retval 电池电压(mV)
 */
uint16_t ADC_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    uint32_t adc_buffer[ADC_SAMPLE_COUNT * 2];  // 2通道采样缓冲区：CH0 + VREFINT
    uint32_t battery_sum = 0;
    uint32_t vrefint_sum = 0;
    uint16_t battery_voltage_mv = 0;

    // 清空缓冲区
    memset(adc_buffer, 0, sizeof(adc_buffer));

    // 校准ADC
    HAL_ADCEx_Calibration_Start(&hadc, ADC_SINGLE_ENDED);

    // 启动DMA采样
    if (HAL_ADC_Start_DMA(&hadc, adc_buffer, ADC_SAMPLE_COUNT * 2) == HAL_OK) {
        HAL_Delay(100);  // 等待采样完成

        // 停止ADC
        HAL_ADC_Stop_DMA(&hadc);

        // 分离通道数据并计算平均值
        // 通道顺序：ADC_CHANNEL_0(电池电压,PA0) -> ADC_CHANNEL_VREFINT
        // 注意：如果硬件连接是PA6，需要在CubeMX中修改为ADC_CHANNEL_6
        for (int i = 0; i < ADC_SAMPLE_COUNT * 2; i += 2) {
            battery_sum += adc_buffer[i];      // ADC_CHANNEL_0 电池电压 (PA0)
            vrefint_sum += adc_buffer[i + 1];  // ADC_CHANNEL_VREFINT 内部参考电压
        }

        // 计算平均值
        uint16_t battery_raw = battery_sum / ADC_SAMPLE_COUNT;
        uint16_t vrefint_raw = vrefint_sum / ADC_SAMPLE_COUNT;

        // 使用VREFINT校正计算实际VDD和电池电压
        if (vrefint_raw > 0) {
            // 计算实际VDD (V)
            float actual_vdd = (float)(ADC_VREFINT_CAL_VREF * ADC_VREFINT_CAL_VALUE) / vrefint_raw / 1000.0f;

            // 计算电池电压（考虑2:1分压电阻）
            float voltage = (battery_raw * actual_vdd / 4096.0f) * 2.0f;

            // 应用用户校准系数和偏移量
            voltage = voltage * BATTERY_VOLTAGE_CALIBRATION_FACTOR + BATTERY_VOLTAGE_OFFSET;

            // 转换为mV
            battery_voltage_mv = (uint16_t)(voltage * 1000);

            // 简化调试输出用于校准
            // printf("ADC: Raw=%d, VRef=%d, VDD=%.3fV\r\n", battery_raw, vrefint_raw, actual_vdd);
        } else {
            printf("VREFINT failed, using fixed VDD\r\n");
            // VREFINT失效时使用固定VDD
            float voltage = (battery_raw * 3.3f / 4096.0f) * 2.0f;
            voltage = voltage * BATTERY_VOLTAGE_CALIBRATION_FACTOR + BATTERY_VOLTAGE_OFFSET;
            battery_voltage_mv = (uint16_t)(voltage * 1000);
        }

    } else {
        printf("ADC sampling failed\r\n");
        battery_voltage_mv = 0;
    }

    return battery_voltage_mv;
}

/**
 * @brief  将mV值转换为V值（兼容性函数）
 * @param  voltage_mv: 电压值(mV)
 * @retval 电池电压值(V)
 */
float ADC_ConvertToVoltage(uint16_t voltage_mv)
{
    return voltage_mv / 1000.0f;
}

// 接收模式标志：0=逐字节接收(配置用), 1=固定长度接收(通信用)
static uint8_t rx_mode = 0;

/**
 * @brief  设置接收模式
 * @param  mode: 0=逐字节接收, 1=固定长度接收
 * @retval None
 */
void E70_SetRxMode(uint8_t mode)
{
    rx_mode = mode;
}

/**
 * @brief  获取当前接收模式
 * @param  None
 * @retval 当前接收模式
 */
uint8_t E70_GetRxMode(void)
{
    return rx_mode;
}

/**
 * @brief  UART接收完成中断回调函数
 * @param  huart: UART句柄指针
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == LPUART1) {
        // 统一使用逐字节接收模式，避免同步问题
        uart_rx_index++;

        // 继续接收下一个字节
        if (uart_rx_index < sizeof(uart_rx_buffer)) {
            HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&uart_rx_buffer[uart_rx_index], 1);
        } else {
            // 缓冲区满了，重新开始
            uart_rx_index = 0;
            HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&uart_rx_buffer[0], 1);
        }
    }
}
